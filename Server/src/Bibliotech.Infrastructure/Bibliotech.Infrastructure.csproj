<Project Sdk="Microsoft.NET.Sdk">
  <ItemGroup>
    <ProjectReference Include="..\Bibliotech.BuildingBlocks\Bibliotech.BuildingBlocks.csproj"/>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.17"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0"/>
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1"/>
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.17"/>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0"/>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1"/>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11"/>
    <PackageReference Include="Serilog" Version="3.1.1"/>
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3"/>
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1"/>
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0"/>
  </ItemGroup>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
</Project>