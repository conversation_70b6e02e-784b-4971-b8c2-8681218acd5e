{"format": 1, "restore": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Modules.Catalog/Bibliotech.Modules.Catalog.csproj": {}}, "projects": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Modules.Catalog/Bibliotech.Modules.Catalog.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Modules.Catalog/Bibliotech.Modules.Catalog.csproj", "projectName": "Bibliotech.Modules.Catalog", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Modules.Catalog/Bibliotech.Modules.Catalog.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Modules.Catalog/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj", "projectName": "Bibliotech.Shared", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}}}