<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.17" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Bibliotech.Infrastructure\Bibliotech.Infrastructure.csproj" />
    <ProjectReference Include="..\Bibliotech.BuildingBlocks\Bibliotech.BuildingBlocks.csproj" />
    <ProjectReference Include="..\Modules\Catalog\Bibliotech.Modules.Catalog.Presentation\Bibliotech.Modules.Catalog.Presentation.csproj" />
    <ProjectReference Include="..\Modules\Catalog\Bibliotech.Modules.Catalog.Infrastructure\Bibliotech.Modules.Catalog.Infrastructure.csproj" />
  </ItemGroup>

</Project>
