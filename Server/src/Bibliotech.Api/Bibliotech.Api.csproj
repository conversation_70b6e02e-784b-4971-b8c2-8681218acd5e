<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.17" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Bibliotech.Shared\Bibliotech.Shared.csproj" />
    <ProjectReference Include="..\Bibliotech.Infrastructure\Bibliotech.Infrastructure.csproj" />
    <ProjectReference Include="..\Bibliotech.Modules.Catalog\Bibliotech.Modules.Catalog.csproj" />
  </ItemGroup>

</Project>
