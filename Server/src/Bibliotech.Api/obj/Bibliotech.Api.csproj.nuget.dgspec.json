{"format": 1, "restore": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Api/Bibliotech.Api.csproj": {}}, "projects": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Api/Bibliotech.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Api/Bibliotech.Api.csproj", "projectName": "Bibliotech.Api", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Api/Bibliotech.Api.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Api/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/Bibliotech.Infrastructure.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/Bibliotech.Infrastructure.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Infrastructure/Bibliotech.Modules.Catalog.Infrastructure.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Infrastructure/Bibliotech.Modules.Catalog.Infrastructure.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Presentation/Bibliotech.Modules.Catalog.Presentation.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Presentation/Bibliotech.Modules.Catalog.Presentation.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.17, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj", "projectName": "Bibliotech.BuildingBlocks", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/Bibliotech.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/Bibliotech.Infrastructure.csproj", "projectName": "Bibliotech.Infrastructure", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/Bibliotech.Infrastructure.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj", "projectName": "Bibliotech.Shared", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/Bibliotech.Shared.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Shared/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj", "projectName": "Bibliotech.Modules.Catalog.Application", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj", "projectName": "Bibliotech.Modules.Catalog.Domain", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Infrastructure/Bibliotech.Modules.Catalog.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Infrastructure/Bibliotech.Modules.Catalog.Infrastructure.csproj", "projectName": "Bibliotech.Modules.Catalog.Infrastructure", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Infrastructure/Bibliotech.Modules.Catalog.Infrastructure.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/Bibliotech.Infrastructure.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Infrastructure/Bibliotech.Infrastructure.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Presentation/Bibliotech.Modules.Catalog.Presentation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Presentation/Bibliotech.Modules.Catalog.Presentation.csproj", "projectName": "Bibliotech.Modules.Catalog.Presentation", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Presentation/Bibliotech.Modules.Catalog.Presentation.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Presentation/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}}}