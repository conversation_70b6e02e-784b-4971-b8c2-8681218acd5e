{"version": 2, "dgSpecHash": "Xmc+J5/pcyEzIZXePDfVbOBc1YT5/WMb5eZ5S4DIMe7AJbTboowItqcR5T1hGtmx+aeIqdJXRYKnfrO2BDQBIA==", "success": true, "projectFilePath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.Api/Bibliotech.Api.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/microsoft.aspnetcore.openapi/8.0.17/microsoft.aspnetcore.openapi.8.0.17.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/6.0.5/microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.openapi/1.6.14/microsoft.openapi.1.6.14.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.6.2/swashbuckle.aspnetcore.6.6.2.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.6.2/swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.6.2/swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.6.2/swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512"], "logs": []}