using Serilog;

var builder = WebApplication.CreateBuilder(args);

Log.Logger = new LoggerConfiguration().ReadFrom.Configuration(builder.Configuration)
          .Enrich.FromLogContext() //To store extra info like User Id, Request Id
          .WriteTo.Console()
          .WriteTo.File("logs/bibliotech.log", rollingInterval: RollingInterval.Day) //Creates new log each day
          .CreateLogger();

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();


var app = builder.Build();

app.UseHttpsRedirection();

app.UseAuthentication(); //I will add it later on second phase

app.MapControllers();

try
{
          Log.Information("Starting Server API");
          app.Run();
}
catch (Exception ex)
{
          Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
          Log.CloseAndFlush();
}

