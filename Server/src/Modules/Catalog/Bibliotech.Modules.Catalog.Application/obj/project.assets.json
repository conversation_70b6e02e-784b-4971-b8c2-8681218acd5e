{"version": 3, "targets": {"net8.0": {"Bibliotech.BuildingBlocks/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/Bibliotech.BuildingBlocks.dll": {}}, "runtime": {"bin/placeholder/Bibliotech.BuildingBlocks.dll": {}}}, "Bibliotech.Modules.Catalog.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/Bibliotech.Modules.Catalog.Domain.dll": {}}, "runtime": {"bin/placeholder/Bibliotech.Modules.Catalog.Domain.dll": {}}}}}, "libraries": {"Bibliotech.BuildingBlocks/1.0.0": {"type": "project", "path": "../../../Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj", "msbuildProject": "../../../Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj"}, "Bibliotech.Modules.Catalog.Domain/1.0.0": {"type": "project", "path": "../Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj", "msbuildProject": "../Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["Bibliotech.BuildingBlocks >= 1.0.0", "Bibliotech.Modules.Catalog.Domain >= 1.0.0"]}, "packageFolders": {"/home/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj", "projectName": "Bibliotech.Modules.Catalog.Application", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotech.Modules.Catalog.Application.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Bibliotech.BuildingBlocks/Bibliotech.BuildingBlocks.csproj"}, "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj": {"projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Domain/Bibliotech.Modules.Catalog.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}}