{"format": 1, "restore": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotecg.Modules.Catalog.Application.csproj": {}}, "projects": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotecg.Modules.Catalog.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotecg.Modules.Catalog.Application.csproj", "projectName": "Bibliotecg.Modules.Catalog.Application", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/Bibliotecg.Modules.Catalog.Application.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/src/Modules/Catalog/Bibliotech.Modules.Catalog.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}}}