﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Bibliotech.Modules.Catalog.Application\Bibliotech.Modules.Catalog.Application.csproj" />
    <ProjectReference Include="..\..\..\Bibliotech.Infrastructure\Bibliotech.Infrastructure.csproj" />
    <ProjectReference Include="..\..\..\Bibliotech.BuildingBlocks\Bibliotech.BuildingBlocks.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.17" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
