{"format": 1, "restore": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/tests/Bibliotech.BuildingBlocks.Tests/Bibliotech.BuildingBlocks.Tests.csproj": {}}, "projects": {"/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/tests/Bibliotech.BuildingBlocks.Tests/Bibliotech.BuildingBlocks.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/tests/Bibliotech.BuildingBlocks.Tests/Bibliotech.BuildingBlocks.Tests.csproj", "projectName": "Bibliotech.BuildingBlocks.Tests", "projectPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/tests/Bibliotech.BuildingBlocks.Tests/Bibliotech.BuildingBlocks.Tests.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/media/kushal/Kushal/Projects/DotNetAndReactNative/Biblitech/Server/tests/Bibliotech.BuildingBlocks.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.6.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}}}